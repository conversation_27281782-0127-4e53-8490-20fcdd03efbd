use sea_orm::{Database, DatabaseConnection, Schema, ConnectionTrait};
use crate::models::target;

pub async fn setup_database() -> Result<DatabaseConnection, sea_orm::DbErr> {
    let db_url = std::env::var("DATABASE_URL").unwrap_or_else(|_| "sqlite::memory:".to_string());
    let db = Database::connect(&db_url).await?;

    // Create table if it doesn't exist
    let builder = db.get_database_backend();
    let schema = Schema::new(builder);
    db.execute(builder.build(schema.create_table_from_entity(target::Entity).if_not_exists())).await?;

    Ok(db)
}
