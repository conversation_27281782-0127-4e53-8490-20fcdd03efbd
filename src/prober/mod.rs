use crate::models::target::Model as Target;
use influxdb2::Client;
use influxdb2::models::DataPoint;
use serde_json::json;
use std::time::Duration;
use surge_ping::Pinger;
use tokio::sync::broadcast;
use tokio::time;
use std::net::IpAddr;

pub async fn run_prober(target: Target, client: Client, tx: broadcast::Sender<String>) {
    let mut interval = time::interval(Duration::from_secs(target.probe_interval_secs as u64));
    loop {
        interval.tick().await;
        let host_ip: IpAddr = target.host.parse().unwrap();
        let pinger = Pinger::new(host_ip).await.unwrap();
        let (pinger_tx, mut pinger_rx) = pinger.pinger(Duration::from_secs(1)).await;
        pinger_tx.ping(0, b"hello").await;
        if let Some(result) = pinger_rx.recv().await {
            let (is_lost, rtt) = match result {
                Ok(reply) => (false, reply.rtt as f64),
                Err(_) => (true, 0.0),
            };

            let point = DataPoint::builder("probe_data")
                .tag("target_id", target.id.to_string())
                .tag("is_lost", is_lost.to_string())
                .field("rtt_ms", rtt)
                .build()
                .unwrap();

            if let Err(e) = client.write("smokeping", futures::stream::iter(vec![point])).await {
                eprintln!("Failed to write to InfluxDB: {}", e);
            }

            let ws_msg = json!({ "target_id": target.id, "is_lost": is_lost, "rtt_ms": rtt }).to_string();
            if let Err(e) = tx.send(ws_msg) {
                eprintln!("Failed to send WebSocket message: {}", e);
            }
        }
    }
}
